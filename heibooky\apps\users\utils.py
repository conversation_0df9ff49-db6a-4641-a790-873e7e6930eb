import logging

import requests
from django.conf import settings
from user_agents import parse

logger = logging.getLogger(__name__)


def get_client_ip(request):
    """Get real client IP considering various headers"""
    PRIVATE_IPS_PREFIX = ("10.", "172.", "192.168.", "127.")

    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR", "")
    if x_forwarded_for:
        proxies = x_forwarded_for.split(",")
        # Get first non-local IP
        for proxy in proxies:
            ip = proxy.strip()
            if not any(ip.startswith(prefix) for prefix in PRIVATE_IPS_PREFIX):
                return ip

    # Try X-Real-IP header
    ip = request.META.get("HTTP_X_REAL_IP", "")
    if ip and not any(ip.startswith(prefix) for prefix in PRIVATE_IPS_PREFIX):
        return ip

    # Fallback to REMOTE_ADDR
    ip = request.META.get("REMOTE_ADDR", "")
    if settings.DEBUG and (not ip or ip.startswith(PRIVATE_IPS_PREFIX)):
        try:
            response = requests.get("https://api.ipify.org?format=json", timeout=2)
            if response.status_code == 200:
                return response.json().get("ip", "")
        except Exception as e:
            logger.debug(f"Could not get external IP: {str(e)}")

    return ip


def get_location_data(ip_address):
    """Enhanced location data retrieval with multiple services"""
    if not ip_address:
        return "Unknown location", None

    services = [
        {
            "url": f"http://ip-api.com/json/{ip_address}",
            "headers": {},
            "mapper": lambda d: {
                "city": d.get("city"),
                "country_name": d.get("country"),
                "region": d.get("regionName"),
                "latitude": d.get("lat"),
                "longitude": d.get("lon"),
                "isp": d.get("isp"),
                "timezone": d.get("timezone"),
            },
            "validator": lambda d: d.get("status") == "success",
        },
        {
            "url": f"https://ipinfo.io/{ip_address}/json",
            "headers": {"Authorization": f"Bearer {settings.IPINFO_TOKEN}"},
            "mapper": lambda d: {
                "city": d.get("city"),
                "country_name": d.get("country"),
                "region": d.get("region"),
                "latitude": (
                    d.get("loc", "").split(",")[0] if "," in d.get("loc", "") else None
                ),
                "longitude": (
                    d.get("loc", "").split(",")[1] if "," in d.get("loc", "") else None
                ),
                "isp": d.get("org"),
                "timezone": d.get("timezone"),
            },
            "validator": lambda d: bool(d.get("city")),
        },
    ]

    for service in services:
        try:
            response = requests.get(
                service["url"], headers=service["headers"], timeout=2
            )
            if response.status_code == 200:
                data = response.json()
                if service["validator"](data):
                    location_str = f"{data.get('city', 'Unknown')}, {data.get('country', 'Unknown')}"
                    return location_str, service["mapper"](data)
        except Exception as e:
            logger.debug(f"Location service {service['url']} failed: {str(e)}")
            continue

    return "Unknown location", None


def get_device_info(user_agent_str):
    """Parse user agent string into device information"""
    try:
        ua = parse(user_agent_str)
        return {
            "device_type": _get_device_type(ua),
            "os": f"{ua.os.family} {ua.os.version_string}",
            "browser": f"{ua.browser.family} {ua.browser.version_string}",
        }
    except Exception as e:
        logger.error(f"User agent parsing failed: {str(e)}")
        return {"device_type": "Unknown"}


def _get_device_type(ua_parse):
    """Determine device type from parsed user agent"""
    if ua_parse.is_mobile:
        return "Mobile"
    if ua_parse.is_tablet:
        return "Tablet"
    if ua_parse.is_pc:
        return "Desktop"
    return "Other"


def should_notify_location_change(previous, current):
    """Improved location change detection"""
    if not previous or not current:
        return False

    def get_location_parts(loc):
        if isinstance(loc, str):
            return loc, None, None
        return (loc.get("location_string"), loc.get("city"), loc.get("country_name"))

    prev_loc, prev_city, prev_country = get_location_parts(previous)
    curr_loc, curr_city, curr_country = get_location_parts(current)

    # Don't notify for unknown locations
    if "Unknown" in (prev_loc, curr_loc):
        return False

    # If we have detailed location data, use it
    if all([prev_city, prev_country, curr_city, curr_country]):
        return prev_city != curr_city or prev_country != curr_country

    # Fallback to string comparison
    return prev_loc != curr_loc


def format_error_response(errors):
    """
    Format validation errors into a structured response.

    Args:
        errors: The error dictionary from serializer.errors or exception

    Returns:
        A structured dictionary with field-specific errors
    """
    if hasattr(errors, "detail"):
        # Handle DRF ValidationError
        return {"errors": {"general": [str(errors.detail)]}}

    if isinstance(errors, dict):
        # Handle serializer.errors dictionary
        formatted_errors = {}
        for field, error_list in errors.items():
            formatted_errors[field] = [str(error) for error in error_list]
        return {"errors": formatted_errors}

    # Handle other exceptions
    return {"errors": {"general": [str(errors)]}}
