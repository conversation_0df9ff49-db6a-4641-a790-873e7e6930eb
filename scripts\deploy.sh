#!/bin/bash
set -e

# Heibooky Zero-Downtime Deployment Script
# Usage: ./deploy.sh [staging|production] [--rollback] [--force]

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="/opt/heibooky/backups"
LOG_FILE="/var/log/heibooky/deploy.log"

# Default values
ENVIRONMENT=""
ROLLBACK=false
FORCE=false
INIT_SSL=false
IMAGE_TAG="${IMAGE_TAG:-latest}"
SSL_DOMAIN="${SSL_DOMAIN:-backend.heibooky.com}"
SSL_EMAIL="${SSL_EMAIL:-<EMAIL>}"
HEALTH_CHECK_TIMEOUT=300
HEALTH_CHECK_INTERVAL=10

# Zero-downtime deployment configuration
CONTAINER_START_TIMEOUT=60
CONTAINER_HEALTH_TIMEOUT=60
CONTAINER_HEALTH_INTERVAL=5
GRACEFUL_STOP_TIMEOUT=30
TRAFFIC_SHIFT_DELAY=10
HEALTH_CHECK_INTERVAL=10

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a "$LOG_FILE"
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            staging|production)
                ENVIRONMENT="$1"
                shift
                ;;
            --rollback)
                ROLLBACK=true
                shift
                ;;
            --force)
                FORCE=true
                shift
                ;;
            --init-ssl)
                INIT_SSL=true
                shift
                ;;
            --image-tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            --ssl-domain)
                SSL_DOMAIN="$2"
                shift 2
                ;;
            --ssl-email)
                SSL_EMAIL="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done

    if [ -z "$ENVIRONMENT" ]; then
        error "Environment must be specified (staging or production)"
        show_help
        exit 1
    fi
}

show_help() {
    cat << EOF
Heibooky Deployment Script

Usage: $0 [staging|production] [OPTIONS]

Options:
    --rollback          Rollback to previous deployment
    --force             Force deployment without confirmation
    --init-ssl          Initialize SSL certificates before deployment
    --image-tag TAG     Specify Docker image tag (default: latest)
    --ssl-domain DOMAIN Specify SSL domain (default: backend.heibooky.com)
    --ssl-email EMAIL   Specify SSL email (default: <EMAIL>)
    --help              Show this help message

Environment Variables:
    SSL_DOMAIN          SSL domain name (overrides default)
    SSL_EMAIL           SSL email address (overrides default)
    IMAGE_TAG           Docker image tag (overrides default)

Examples:
    $0 staging
    $0 production --force
    $0 production --rollback
    $0 production --init-ssl
    $0 staging --image-tag v1.2.3
    $0 production --init-ssl --ssl-domain example.com --ssl-email <EMAIL>
    SSL_DOMAIN=example.com SSL_EMAIL=<EMAIL> $0 production --init-ssl
EOF
}
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if running as correct user (restrict to a fixed set of service accounts)
    ALLOWED_USERS=("ubuntu" "ec2-user" "deploy")
    if [[ ! " ${ALLOWED_USERS[@]} " =~ " ${USER} " ]] && [ "$FORCE" != true ]; then
        error "This script should be run as one of: ${ALLOWED_USERS[*]}. Use --force to override."
        exit 1
    fi
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        error "Docker is not running"
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check if Doppler is installed
    if ! command -v doppler &> /dev/null; then
        error "Doppler CLI is not installed"
        exit 1
    fi
    
    # Verify Doppler authentication
    if ! doppler me &> /dev/null; then
        error "Doppler is not authenticated. Please run 'doppler login' first."
        exit 1
    fi
    
    log "Prerequisites check passed"
}

# Set Doppler configuration based on environment
setup_doppler_config() {
    log "Setting up Doppler configuration for $ENVIRONMENT..."

    case $ENVIRONMENT in
        staging)
            export DOPPLER_PROJECT="heibooky-backend"
            export DOPPLER_CONFIG="stg"
            ;;
        production)
            export DOPPLER_PROJECT="heibooky-backend"
            export DOPPLER_CONFIG="prd"
            ;;
        *)
            error "Unknown environment: $ENVIRONMENT"
            exit 1
            ;;
    esac

    log "Doppler configured: Project=$DOPPLER_PROJECT, Config=$DOPPLER_CONFIG"
}

# Initialize SSL certificates
initialize_ssl() {
    log "Initializing SSL certificates..."

    # Check if SSL initialization script exists
    if [ ! -f "scripts/init-ssl.sh" ]; then
        error "SSL initialization script not found: scripts/init-ssl.sh"
        exit 1
    fi

    # Validate SSL configuration
    if [ -z "$SSL_DOMAIN" ] || [ -z "$SSL_EMAIL" ]; then
        error "SSL domain and email must be specified"
        error "Set SSL_DOMAIN and SSL_EMAIL environment variables or use --ssl-domain and --ssl-email options"
        exit 1
    fi

    # Make script executable
    chmod +x scripts/init-ssl.sh

    # Run SSL initialization
    if [ "$ENVIRONMENT" = "production" ]; then
        log "Running SSL initialization for production..."
        log "Domain: $SSL_DOMAIN, Email: $SSL_EMAIL"
        ./scripts/init-ssl.sh --domain "$SSL_DOMAIN" --email "$SSL_EMAIL"
    else
        log "Running SSL initialization for staging (using staging certificates)..."
        log "Domain: $SSL_DOMAIN, Email: $SSL_EMAIL"
        ./scripts/init-ssl.sh --domain "$SSL_DOMAIN" --email "$SSL_EMAIL" --staging
    fi

    log "SSL initialization completed"
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    local backup_timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_name="backup_${ENVIRONMENT}_${backup_timestamp}"
    local backup_path="${BACKUP_DIR}/${backup_name}"
    
    mkdir -p "$backup_path"
    
    # Backup current docker-compose file
    if [ -f "docker-compose.prod.yml" ]; then
        cp docker-compose.prod.yml "$backup_path/"
    fi
    
    # Backup environment configuration
    # First check if GPG is available and configured for non-interactive use
    log "Attempting to backup secrets..."
    if command -v gpg &> /dev/null && [ -n "$GPG_PASSPHRASE" ]; then
        # Use GPG with passphrase from environment variable
        doppler secrets download --project="$DOPPLER_PROJECT" --config="$DOPPLER_CONFIG" --format env | \
        gpg --batch --yes --passphrase="$GPG_PASSPHRASE" --symmetric --cipher-algo AES256 --output "$backup_path/.env.backup.gpg" 2>/dev/null && \
        log "Secrets backup encrypted successfully" || {
            warn "GPG encryption failed, creating unencrypted backup"
            doppler secrets download --project="$DOPPLER_PROJECT" --config="$DOPPLER_CONFIG" --format env > "$backup_path/.env.backup" 2>/dev/null || \
            warn "Failed to create secrets backup"
        }
    else
        warn "GPG not available or no passphrase set, creating unencrypted backup"
        doppler secrets download --project="$DOPPLER_PROJECT" --config="$DOPPLER_CONFIG" --format env > "$backup_path/.env.backup" 2>/dev/null || \
        warn "Failed to create secrets backup"
    fi
    # Backup database (if using local database)
    if docker-compose -f docker-compose.prod.yml ps postgres &> /dev/null; then
        log "Backing up database..."
        docker-compose -f docker-compose.prod.yml exec -T postgres pg_dump -U postgres heibooky > "$backup_path/database.sql"
    fi
    
    # Store current image tags
    docker-compose -f docker-compose.prod.yml config | grep "image:" > "$backup_path/images.txt"
    
    echo "$backup_name" > "${BACKUP_DIR}/latest_backup.txt"
    
    log "Backup created: $backup_name"
}

# Health check function
# Comprehensive health check function
health_check() {
    local service_url="$1"
    local timeout="$2"
    local interval="$3"

    log "Performing comprehensive health check..."

    local elapsed=0
    while [ $elapsed -lt $timeout ]; do
        local checks_passed=0
        local total_checks=0

        # Check 1: Docker health status
        total_checks=$((total_checks + 1))
        if docker inspect --format='{{.State.Health.Status}}' heibooky-web 2>/dev/null | grep -q "healthy"; then
            checks_passed=$((checks_passed + 1))
            info "✓ Docker health check passed"
        else
            info "✗ Docker health check failed"
        fi

        # Check 2: Direct Django health check
        total_checks=$((total_checks + 1))
        if docker exec heibooky-web curl -f -s "http://localhost:8000/health/" > /dev/null 2>&1; then
            checks_passed=$((checks_passed + 1))
            info "✓ Direct Django health check passed"
        else
            info "✗ Direct Django health check failed"
        fi

        # Check 3: Nginx health check (if nginx is running)
        if docker ps --format "table {{.Names}}" | grep -q "heibooky-nginx"; then
            total_checks=$((total_checks + 1))
            if curl -f -s -H "Host: localhost" "$service_url/health/" > /dev/null 2>&1; then
                checks_passed=$((checks_passed + 1))
                info "✓ Nginx health check passed"
            else
                info "✗ Nginx health check failed"
            fi
        fi

        # If at least 2 out of 3 checks pass, consider it healthy
        if [ $checks_passed -ge 2 ] || [ $checks_passed -eq $total_checks ]; then
            log "Health check passed ($checks_passed/$total_checks checks successful)"
            return 0
        fi

        info "Health check failed ($checks_passed/$total_checks checks passed), retrying in ${interval}s... (${elapsed}/${timeout}s elapsed)"
        sleep $interval
        elapsed=$((elapsed + interval))
    done

    error "Health check failed after ${timeout}s"
    return 1
}

# Check Docker container health status
check_docker_health() {
    local container_name="$1"
    local timeout="${2:-60}"
    local interval="${3:-5}"

    log "Checking Docker health status for $container_name..."

    local elapsed=0
    while [ $elapsed -lt $timeout ]; do
        local health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "none")

        if [ "$health_status" = "healthy" ]; then
            log "Container $container_name is healthy"
            return 0
        elif [ "$health_status" = "none" ]; then
            warn "Container $container_name has no health check configured"
            return 0
        fi

        info "Container $container_name health status: $health_status, retrying in ${interval}s... (${elapsed}/${timeout}s elapsed)"
        sleep $interval
        elapsed=$((elapsed + interval))
    done

    error "Container $container_name failed health check after ${timeout}s"
    return 1
}

# Check specific container health
check_container_health() {
    local container_name="$1"
    local timeout="${2:-60}"
    local interval="${3:-5}"
    
    log "Checking health of container: $container_name"
    
    local elapsed=0
    while [ $elapsed -lt $timeout ]; do
        local health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "unknown")
        
        if [ "$health_status" = "healthy" ]; then
            log "Container $container_name is healthy"
            return 0
        elif [ "$health_status" = "unhealthy" ]; then
            error "Container $container_name is unhealthy"
            return 1
        fi
        
        info "Container $container_name health status: $health_status, waiting... (${elapsed}/${timeout}s elapsed)"
        sleep $interval
        elapsed=$((elapsed + interval))
    done
    
    error "Container $container_name health check timeout"
    return 1
}

# Get running web container IDs
get_web_containers() {
    docker-compose -f docker-compose.prod.yml ps -q web 2>/dev/null || echo ""
}

# Get container port mapping
get_container_port() {
    local container_id="$1"
    docker port "$container_id" 8000 2>/dev/null | head -n1 | cut -d: -f2
}

# Test container endpoint
test_container_endpoint() {
    local container_id="$1"
    local port=$(get_container_port "$container_id")
    
    if [ -z "$port" ]; then
        warn "Could not determine port for container $container_id"
        return 1
    fi
    
    local endpoint="http://localhost:$port"
    log "Testing container endpoint: $endpoint"
    
    # Test health endpoint with timeout
    if timeout 10s curl -f -s "$endpoint/health/" > /dev/null 2>&1; then
        log "Container $container_id endpoint test passed"
        return 0
    else
        warn "Container $container_id endpoint test failed"
        return 1
    fi
}

# Pre-deployment validation
validate_deployment() {
    log "Validating deployment prerequisites..."
    
    # Check if docker-compose.prod.yml exists
    if [ ! -f "docker-compose.prod.yml" ]; then
        error "docker-compose.prod.yml not found"
        return 1
    fi
    
    # Validate docker-compose file syntax
    if ! docker-compose -f docker-compose.prod.yml config > /dev/null 2>&1; then
        error "Invalid docker-compose.prod.yml syntax"
        return 1
    fi
    
    # Check if required services are defined
    local required_services="web redis"
    for service in $required_services; do
        if ! docker-compose -f docker-compose.prod.yml config --services | grep -q "^${service}$"; then
            error "Required service '$service' not found in docker-compose.prod.yml"
            return 1
        fi
    done
    
    # Check Docker system resources
    local available_memory=$(docker system df --format "{{.Reclaimable}}" | head -n1 | sed 's/[^0-9.]//g')
    local available_disk=$(df / | awk 'NR==2 {print $4}')
    
    log "Docker system check - Available disk: ${available_disk}KB"
    
    # Verify Doppler configuration (already set by setup_doppler_config)
    if ! doppler configs --project="$DOPPLER_PROJECT" | grep -q "$DOPPLER_CONFIG"; then
        warn "Doppler config '$DOPPLER_CONFIG' not found in project '$DOPPLER_PROJECT', deployment may fail"
    fi
    
    log "Pre-deployment validation completed"
    return 0
}

# Deploy function with blue-green strategy
deploy() {
    log "Starting zero-downtime deployment to $ENVIRONMENT..."
    
    # Get current running containers
    local current_containers=$(get_web_containers)
    local current_count=$(echo "$current_containers" | wc -w)
    
    if [ $current_count -eq 0 ]; then
        log "No running containers found, performing initial deployment"
        deploy_initial
        return $?
    fi
    
    log "Found $current_count running web container(s), performing rolling update"
    
    # Pull latest images without affecting running containers
    log "Pulling latest Docker images..."
    export IMAGE_TAG="$IMAGE_TAG"
    docker-compose -f docker-compose.prod.yml pull web
    
    # Perform rolling deployment
    deploy_rolling_update "$current_count"
}

# Initial deployment (no existing containers)
deploy_initial() {
    log "Performing initial deployment..."

    # Start all services
    doppler run -- docker-compose -f docker-compose.prod.yml up -d

    # Wait for containers to start
    sleep 15

    # Determine health check URL based on SSL certificate availability
    local health_url="http://localhost"
    if [ -f "./certbot/conf/live/backend.heibooky.com/fullchain.pem" ]; then
        health_url="https://localhost"
        log "SSL certificates found, using HTTPS for health checks"
    else
        log "No SSL certificates found, using HTTP for health checks"
    fi

    # Health check
    if ! health_check "$health_url" "$HEALTH_CHECK_TIMEOUT" "$HEALTH_CHECK_INTERVAL"; then
        error "Initial deployment health check failed"
        return 1
    fi

    log "Initial deployment completed successfully!"
    return 0
}

# Rolling update deployment
deploy_rolling_update() {
    local current_count="$1"
    local target_count=$((current_count + 1))
    
    log "Starting rolling update: $current_count -> $target_count containers"
    
    # Step 1: Scale up with new containers (blue-green approach)
    log "Scaling up to $target_count containers with new image..."
    doppler run -- docker-compose -f docker-compose.prod.yml up -d --scale web=$target_count --no-deps --no-recreate
    
    # Wait for new containers to start
    sleep 20
    
    # Step 2: Get all current containers and identify new ones
    local all_containers=$(get_web_containers)
    local new_containers=""
    
    log "Identifying and testing new containers..."
    for container in $all_containers; do
        # Check if this is a newly created container (created in last 2 minutes)
        local created=$(docker inspect --format='{{.Created}}' "$container")
        local created_timestamp=$(date -d "$created" +%s 2>/dev/null || echo "0")
        local current_timestamp=$(date +%s)
        local age=$((current_timestamp - created_timestamp))
        
        if [ $age -lt $CONTAINER_START_TIMEOUT ]; then  # Less than timeout seconds old
            new_containers="$new_containers $container"
            log "Found new container: $container (age: ${age}s)"
        fi
    done
    
    # Step 3: Health check new containers individually
    local healthy_new_containers=""
    for container in $new_containers; do
        log "Testing new container: $container"
        
        # Wait for container to be ready
        if check_container_health "$container" "$CONTAINER_HEALTH_TIMEOUT" "$CONTAINER_HEALTH_INTERVAL"; then
            if test_container_endpoint "$container"; then
                healthy_new_containers="$healthy_new_containers $container"
                log "New container $container is healthy and ready"
            else
                warn "New container $container failed endpoint test"
            fi
        else
            warn "New container $container failed health check"
        fi
    done
    
    # Step 4: Verify we have healthy new containers
    local healthy_count=$(echo "$healthy_new_containers" | wc -w)
    if [ $healthy_count -eq 0 ]; then
        error "No healthy new containers found, rolling back..."
        
        # Scale back down to original count
        doppler run -- docker-compose -f docker-compose.prod.yml up -d --scale web=$current_count --no-deps
        return 1
    fi
    
    log "Successfully started $healthy_count new healthy container(s)"
    
    # Step 5: Gradually remove old containers
    log "Starting gradual removal of old containers..."
    
    local old_containers=""
    for container in $all_containers; do
        if ! echo "$new_containers" | grep -q "$container"; then
            old_containers="$old_containers $container"
        fi
    done
    
    # Remove old containers one by one with health checks
    for old_container in $old_containers; do
        log "Removing old container: $old_container"
        
        # Stop the old container gracefully
        docker stop "$old_container" --time="$GRACEFUL_STOP_TIMEOUT"
        
        # Wait a moment for traffic to shift
        sleep "$TRAFFIC_SHIFT_DELAY"
        
        # Verify service is still healthy
        if ! health_check "http://localhost" 60 5; then
            error "Service became unhealthy after removing $old_container, attempting recovery..."
            
            # Try to restart the old container
            docker start "$old_container" || true
            sleep 10
            
            # Check if recovery worked
            if ! health_check "http://localhost" 60 5; then
                error "Recovery failed, manual intervention required"
                return 1
            fi
            
            warn "Recovery successful, but deployment incomplete"
            return 1
        fi
        
        # Remove the stopped container
        docker rm "$old_container" || true
        log "Successfully removed old container: $old_container"
    done
    
    # Step 6: Final health check and cleanup
    log "Performing final health check..."
    if ! health_check "http://localhost" "$HEALTH_CHECK_TIMEOUT" "$HEALTH_CHECK_INTERVAL"; then
        error "Final health check failed"
        return 1
    fi
    
    # Step 7: Scale down to desired count if we have extras
    local final_containers=$(get_web_containers)
    local final_count=$(echo "$final_containers" | wc -w)
    
    if [ $final_count -gt $current_count ]; then
        log "Scaling down to original count: $final_count -> $current_count"
        doppler run -- docker-compose -f docker-compose.prod.yml up -d --scale web=$current_count --no-deps
        
        # Final verification
        sleep 10
        if ! health_check "http://localhost" 60 5; then
            error "Final scale-down health check failed"
            return 1
        fi
    fi
    
    log "Rolling deployment completed successfully!"
    return 0
}

# Rollback function with zero-downtime strategy
rollback_deployment() {
    log "Starting zero-downtime rollback..."
    
    local latest_backup=$(cat "${BACKUP_DIR}/latest_backup.txt" 2>/dev/null || echo "")
    
    if [ -z "$latest_backup" ]; then
        error "No backup found for rollback"
        exit 1
    fi
    
    local backup_path="${BACKUP_DIR}/${latest_backup}"
    
    if [ ! -d "$backup_path" ]; then
        error "Backup directory not found: $backup_path"
        exit 1
    fi
    
    log "Rolling back to backup: $latest_backup"
    
    # Restore docker-compose file
    if [ -f "$backup_path/docker-compose.prod.yml" ]; then
        cp "$backup_path/docker-compose.prod.yml" .
    fi
    
    # Get current container count for rolling strategy
    local current_containers=$(get_web_containers)
    local current_count=$(echo "$current_containers" | wc -w)
    
    if [ $current_count -eq 0 ]; then
        log "No running containers, performing direct rollback"
        # Direct rollback when no containers are running
        doppler run -- docker-compose -f docker-compose.prod.yml up -d
        
        if ! health_check "http://localhost" "$HEALTH_CHECK_TIMEOUT" "$HEALTH_CHECK_INTERVAL"; then
            error "Rollback health check failed"
            exit 1
        fi
        
        log "Direct rollback completed successfully!"
        return 0
    fi
    
    log "Performing rolling rollback with $current_count containers"
    
    # Restore and pull rollback images
    local rollback_images=()
    if [ -f "$backup_path/images.txt" ]; then
        while IFS= read -r line; do
            if [[ $line == *"image:"* ]]; then
                local image=$(echo "$line" | sed 's/.*image: //' | tr -d '"')
                log "Pulling rollback image: $image"
                docker pull "$image"
                rollback_images+=("$image")
            fi
        done < "$backup_path/images.txt"
    fi
    
    # Set the rollback image tag
    if [ ${#rollback_images[@]} -gt 0 ]; then
        # Extract tag from the web service image
        for image in "${rollback_images[@]}"; do
            if [[ $image == *"web"* ]] || [[ $image == *"heibooky"* ]]; then
                export IMAGE_TAG=$(echo "$image" | sed 's/.*://')
                break
            fi
        done
    fi
    
    # Perform rolling rollback using the same strategy as deployment
    local target_count=$((current_count + 1))
    
    log "Scaling up with rollback containers: $current_count -> $target_count"
    doppler run -- docker-compose -f docker-compose.prod.yml up -d --scale web=$target_count --no-deps --no-recreate
    
    # Wait for rollback containers to start
    sleep 20
    
    # Test new rollback containers
    local all_containers=$(get_web_containers)
    local healthy_rollback_containers=""
    
    for container in $all_containers; do
        local created=$(docker inspect --format='{{.Created}}' "$container")
        local created_timestamp=$(date -d "$created" +%s 2>/dev/null || echo "0")
        local current_timestamp=$(date +%s)
        local age=$((current_timestamp - created_timestamp))
        
        if [ $age -lt $CONTAINER_START_TIMEOUT ]; then  # New rollback container
            log "Testing rollback container: $container"
            
            if check_container_health "$container" "$CONTAINER_HEALTH_TIMEOUT" "$CONTAINER_HEALTH_INTERVAL" && test_container_endpoint "$container"; then
                healthy_rollback_containers="$healthy_rollback_containers $container"
                log "Rollback container $container is healthy"
            else
                warn "Rollback container $container failed health check"
            fi
        fi
    done
    
    # Verify rollback containers are healthy
    local healthy_count=$(echo "$healthy_rollback_containers" | wc -w)
    if [ $healthy_count -eq 0 ]; then
        error "No healthy rollback containers found"
        exit 1
    fi
    
    log "Successfully started $healthy_count healthy rollback container(s)"
    
    # Remove old containers gradually
    local old_containers=""
    for container in $all_containers; do
        if ! echo "$healthy_rollback_containers" | grep -q "$container"; then
            old_containers="$old_containers $container"
        fi
    done
    
    for old_container in $old_containers; do
        log "Removing problematic container: $old_container"
        docker stop "$old_container" --time="$GRACEFUL_STOP_TIMEOUT"
        
        sleep "$TRAFFIC_SHIFT_DELAY"
        
        if ! health_check "http://localhost" 30 5; then
            error "Rollback health check failed after removing $old_container"
            exit 1
        fi
        
        docker rm "$old_container" || true
    done
    
    # Scale back to original count
    if [ $healthy_count -gt $current_count ]; then
        log "Scaling down to original count: $healthy_count -> $current_count"
        doppler run -- docker-compose -f docker-compose.prod.yml up -d --scale web=$current_count --no-deps
    fi
    
    # Final health check
    if ! health_check "http://localhost" "$HEALTH_CHECK_TIMEOUT" "$HEALTH_CHECK_INTERVAL"; then
        error "Final rollback health check failed"
        exit 1
    fi
    
    log "Zero-downtime rollback completed successfully!"
}

# Cleanup old backups (keep last 10)
cleanup_backups() {
    log "Cleaning up old backups..."
    
    if [ -d "$BACKUP_DIR" ]; then
        cd "$BACKUP_DIR"
        # Safer deletion using find
        find . -maxdepth 1 -type d -name "backup_*" | sort -r | tail -n +11 | while IFS= read -r dir; do
            rm -rf -- "$dir"
        done
        log "Backup cleanup completed"
    fi
}

# Main execution
main() {
    # Create log directory
    mkdir -p "$(dirname "$LOG_FILE")"
    mkdir -p "$BACKUP_DIR"
    
    log "=== Heibooky Deployment Started ==="
    log "Environment: $ENVIRONMENT"
    log "Image Tag: $IMAGE_TAG"
    log "Rollback: $ROLLBACK"
    log "Force: $FORCE"
    log "Initialize SSL: $INIT_SSL"
    if [ "$INIT_SSL" = true ]; then
        log "SSL Domain: $SSL_DOMAIN"
        log "SSL Email: $SSL_EMAIL"
    fi

    # Change to project directory
    cd "$PROJECT_DIR"

    # Check prerequisites
    check_prerequisites

    # Setup Doppler configuration
    setup_doppler_config

    # Initialize SSL certificates if requested
    if [ "$INIT_SSL" = true ]; then
        initialize_ssl
    fi
    
    if [ "$ROLLBACK" = true ]; then
        rollback_deployment
    else
        # Validation before deployment
        if ! validate_deployment; then
            error "Pre-deployment validation failed"
            exit 1
        fi
        
        # Confirmation for production
        if [ "$ENVIRONMENT" = "production" ] && [ "$FORCE" != true ]; then
            echo -n "Are you sure you want to deploy to PRODUCTION? (yes/no): "
            read -r confirmation
            if [ "$confirmation" != "yes" ]; then
                log "Deployment cancelled by user"
                exit 0
            fi
        fi
        
        create_backup
        deploy
        cleanup_backups
    fi
    
    log "=== Heibooky Deployment Completed ==="
}

# Parse arguments and run main function
parse_args "$@"
main
