import logging
import uuid
from functools import wraps

from apps.billing.utils import check_billing_complete
from apps.pricing.models import RatePlan
from apps.stay.models import (
    Photo,
    Property,
    PropertyAmenity,
    PropertyOwnership,
    Room,
    StaffRole,
)
from django.core.exceptions import ValidationError
from rest_framework import status
from rest_framework.response import Response

logger = logging.getLogger(__name__)


def validate_ownership(user, property_id: str, include_staff: bool = False):
    try:
        # Validate property exists
        if not Property.objects.filter(id=property_id).exists():
            raise ValidationError("Property not found")

        # Check ownership through PropertyOwnership model
        has_ownership = PropertyOwnership.objects.filter(
            user=user,
            property_id=property_id,
        ).exists()

        # If include_staff is True, also check if user is in property's staff
        if include_staff and not has_ownership:
            has_staff_access = Property.objects.filter(
                id=property_id, staffs=user
            ).exists()
            return has_ownership or has_staff_access

        return has_ownership

    except Property.DoesNotExist:
        raise ValidationError("Property not found")

    except Exception as e:
        logger.error(f"Ownership validation error: {str(e)}")
        return False


def require_property_ownership(include_staff: bool = False):
    """
    Decorator for property ownership verification with error handling.
    """

    def decorator(view_func):
        @wraps(view_func)
        def wrapper(self, request, *args, **kwargs):
            try:
                property_id = (
                    request.data.get("property_id")
                    or request.query_params.get("property_id")
                    or kwargs.get("property_id")
                )

                if not property_id:
                    return Response(
                        {"error": "Property ID is required"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                try:
                    uuid.UUID(str(property_id), version=4)
                except ValueError:
                    return Response(
                        {"error": "Invalid property ID format"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                if not validate_ownership(request.user, property_id, include_staff):
                    return Response(
                        {"error": "Insufficient permissions"},
                        status=status.HTTP_403_FORBIDDEN,
                    )

                return view_func(self, request, *args, **kwargs)

            except Exception as e:
                logger.error(f"Property ownership check error: {str(e)}")
                return Response(
                    {"error": "Internal server error"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        return wrapper

    return decorator


def cleaning_staff_check(property, user):
    """
    Check if user is a cleaning staff for the property.
    """
    return StaffRole.objects.filter(
        user=user, property=property, permissions__name="cleaning_staff", is_active=True
    ).exists()


def check_status(property_instance, user, is_owner=False):
    """
    Check if status is valid.
    """
    # Check billing information
    if is_owner:
        billing_complete = check_billing_complete(user)
    else:
        billing_complete = True

    # Calculate status for each section
    metadata_status = (
        hasattr(property_instance, "metadata")
        and property_instance.metadata.regional_id_code
        and len(property_instance.metadata.regional_id_code.strip()) > 0
    )

    location_status = bool(
        property_instance.location
        and all(
            [
                property_instance.location.street,
                property_instance.location.post_code,
                property_instance.location.city,
                property_instance.location.country,
            ]
        )
    )

    amenities_status = PropertyAmenity.objects.filter(
        property=property_instance
    ).exists()

    rooms_status = Room.objects.filter(
        property=property_instance,
    ).exists()

    photos_count = Photo.objects.filter(property=property_instance).count()
    photos_status = photos_count >= 5

    guest_arrival_status = hasattr(property_instance, "guest_arrival_info")

    rates_status = RatePlan.objects.filter(
        property=property_instance,
    ).exists()

    status_data = {
        "general_info": metadata_status,
        "location": location_status,
        "amenities": amenities_status,
        "rooms": rooms_status,
        "pictures": photos_status,
        "guest_arrival": guest_arrival_status,
        "rates": rates_status,
        "billing_complete": billing_complete,
    }
    return status_data


def approve_invite(invite, user):
    if invite.accepted or invite.is_expired():
        raise ValidationError({"invite": "Invalid or expired invite"})

    if user.email != invite.email:
        raise ValidationError({"invite": "This invite was sent to a different email"})

    # Create and save the StaffRole instance
    staff_role = StaffRole.objects.create(
        property=invite.property, user=user, is_active=True
    )

    staff_role.permissions.set(invite.permissions.all())
    invite.property.staffs.add(user)

    # Delete the invite instead of marking it as accepted
    invite.delete()

    logger.info(f"Team invite accepted and deleted for {user.email}")
    return True
