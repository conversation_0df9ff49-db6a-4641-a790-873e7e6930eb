# Monitoring and Health Check Troubleshooting Guide

## Issues Fixed

### 1. ❌ Missing `collect_celery_metrics` Method
**Error**: `'MetricsCollector' object has no attribute 'collect_celery_metrics'`

**Root Cause**: The `MetricsCollector` class was missing the `collect_celery_metrics` method that was being called in `collect_all_metrics()`.

**Solution**: ✅ Added the missing method to properly collect Celery metrics including worker count and queue lengths.

### 2. ❌ Health Check 301 Redirect
**Error**: `127.0.0.1:49644 - - [03/Jul/2025:21:12:47] "GET /health/" 301 -`

**Root Cause**: <PERSON>in<PERSON> was redirecting health check requests to HTTPS, causing monitoring tools to receive redirects instead of health status.

**Solution**: ✅ Updated nginx configuration to handle health checks on both HTTP and HTTPS without redirects.

## Testing the Fixes

### 1. Test Health Check Endpoints

```bash
# Make scripts executable
chmod +x scripts/*.sh

# Test all health check endpoints
./scripts/test-health-checks.sh

# Test with specific URL
./scripts/test-health-checks.sh --url https://backend.heibooky.com
```

### 2. Monitor Application in Real-Time

```bash
# Start monitoring dashboard
./scripts/monitoring-dashboard.sh

# Monitor with custom settings
./scripts/monitoring-dashboard.sh --url https://backend.heibooky.com --interval 10
```

### 3. Manual Health Check Testing

```bash
# Test root health check
curl -v http://localhost/health/
curl -v https://backend.heibooky.com/health/

# Test monitoring endpoints
curl -v http://localhost/monitoring/health/
curl -v http://localhost/monitoring/ready/
curl -v http://localhost/monitoring/alive/

# Test metrics endpoint
curl -v http://localhost/monitoring/metrics/
```

## Health Check Endpoints

| Endpoint | Purpose | Expected Response |
|----------|---------|-------------------|
| `/health/` | Overall application health | `{"status": "healthy", "checks": {...}}` |
| `/monitoring/health/` | Detailed health with all checks | `{"status": "healthy", "checks": {...}}` |
| `/monitoring/ready/` | Kubernetes readiness probe | `{"status": "ready"}` |
| `/monitoring/alive/` | Kubernetes liveness probe | `{"status": "alive"}` |
| `/monitoring/metrics/` | Prometheus metrics | Prometheus format text |
| `/monitoring/metrics/debug/` | Metrics in JSON format | `{"health": {...}, "collection_status": "ok"}` |

## Common Issues and Solutions

### Issue: Health Check Returns 503
**Symptoms**: Health check endpoints return HTTP 503 status

**Possible Causes**:
1. Database connection issues
2. Cache (Redis) connectivity problems
3. Celery workers not running

**Solutions**:
```bash
# Check database connectivity
docker-compose -f docker-compose.prod.yml exec web python manage.py dbshell

# Check Redis connectivity
docker-compose -f docker-compose.prod.yml exec redis redis-cli ping

# Check Celery workers
docker-compose -f docker-compose.prod.yml logs celery
```

### Issue: Metrics Collection Errors
**Symptoms**: Errors in logs about metrics collection

**Possible Causes**:
1. Missing Celery connection
2. Database timeout
3. Cache connectivity issues

**Solutions**:
```bash
# Check Celery broker connectivity
docker-compose -f docker-compose.prod.yml exec web python -c "
from heibooky.celery import app
inspect = app.control.inspect()
print('Active workers:', inspect.active())
"

# Check metrics collection manually
docker-compose -f docker-compose.prod.yml exec web python -c "
from apps.monitoring.metrics import metrics_collector
metrics_collector.collect_all_metrics()
print('Metrics collected successfully')
"
```

### Issue: 301 Redirects on Health Checks
**Symptoms**: Health checks return 301 redirects instead of 200 OK

**Solution**: ✅ Already fixed in nginx configuration. Health checks now bypass HTTPS redirects.

### Issue: Container Health Check Failures
**Symptoms**: Docker health checks failing

**Solutions**:
```bash
# Check container health status
docker-compose -f docker-compose.prod.yml ps

# View health check logs
docker inspect heibooky-web | jq '.[0].State.Health'

# Test health check manually
docker-compose -f docker-compose.prod.yml exec web curl -f http://localhost:8000/health/ || exit 1
```

## Monitoring Best Practices

### 1. Set Up Automated Monitoring
```bash
# Add to crontab for regular health checks
*/5 * * * * /opt/heibooky/scripts/test-health-checks.sh --url https://backend.heibooky.com >> /var/log/heibooky/health-check.log 2>&1
```

### 2. Configure Alerting
- Set up alerts for health check failures
- Monitor metrics endpoint for application performance
- Alert on container restart events

### 3. Log Monitoring
```bash
# Monitor application logs
tail -f /var/log/heibooky/deploy.log
docker-compose -f docker-compose.prod.yml logs -f web

# Monitor nginx access logs
docker-compose -f docker-compose.prod.yml logs -f nginx
```

## Debugging Commands

### Application Debugging
```bash
# Check Django settings
docker-compose -f docker-compose.prod.yml exec web python manage.py check

# Run Django shell
docker-compose -f docker-compose.prod.yml exec web python manage.py shell

# Check database migrations
docker-compose -f docker-compose.prod.yml exec web python manage.py showmigrations
```

### Container Debugging
```bash
# Check container resource usage
docker stats

# Inspect container configuration
docker inspect heibooky-web

# Check container logs with timestamps
docker-compose -f docker-compose.prod.yml logs -t web
```

### Network Debugging
```bash
# Test internal container connectivity
docker-compose -f docker-compose.prod.yml exec web curl http://redis:6379
docker-compose -f docker-compose.prod.yml exec web curl http://db:5432

# Check port bindings
docker-compose -f docker-compose.prod.yml port nginx 80
docker-compose -f docker-compose.prod.yml port nginx 443
```

## Performance Monitoring

### Key Metrics to Monitor
1. **Response Time**: Health check response times
2. **Error Rate**: 4xx/5xx error rates
3. **Resource Usage**: CPU, memory, disk usage
4. **Database Performance**: Query times, connection counts
5. **Cache Performance**: Hit/miss ratios
6. **Celery Performance**: Task completion rates, queue lengths

### Prometheus Metrics Available
- `django_request_count_total`: Total HTTP requests
- `django_request_latency_seconds`: Request latency
- `django_database_connections_total`: Database connections
- `celery_tasks_total`: Celery task counts
- `celery_workers_total`: Active Celery workers
- `django_process_memory_usage_bytes`: Memory usage
- `django_process_cpu_percent`: CPU usage

## Emergency Procedures

### If Health Checks Fail
1. Check container status: `docker-compose -f docker-compose.prod.yml ps`
2. Review recent logs: `docker-compose -f docker-compose.prod.yml logs --tail=50 web`
3. Restart unhealthy services: `docker-compose -f docker-compose.prod.yml restart web`
4. If database issues: Check database connectivity and disk space
5. If cache issues: Restart Redis container

### If Metrics Collection Fails
1. Check Celery worker status
2. Verify database connectivity
3. Restart metrics collection: Restart web container
4. Check for resource constraints (CPU, memory)

This troubleshooting guide should help you quickly identify and resolve monitoring issues in your Heibooky deployment.
