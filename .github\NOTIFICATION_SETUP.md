# CI/CD Notification Setup

This document describes the notification system implemented in the Heibooky CI/CD pipeline.

## Overview

The pipeline now sends real notifications via email and Slack when deployments succeed or fail, replacing the previous echo-only approach.

## Email Notifications

### Configuration Required

To enable email notifications, you need to set up the following GitHub repository secrets:

#### Required Secrets

1. **SMTP_USERNAME** - Your Gmail address (e.g., `<EMAIL>`)
2. **SMTP_PASSWORD** - Gmail app password (not your regular password)

#### Setting up Gmail App Password

1. Go to your Google Account settings
2. Navigate to Security → 2-Step Verification
3. At the bottom, select "App passwords"
4. Generate a new app password for "Mail"
5. Use this 16-character password as `SMTP_PASSWORD`

### Email Features

#### Success Notifications
- ✅ Clean, professional HTML template
- Deployment details (environment, branch, commit, etc.)
- Direct links to the deployed application
- Quick access links to admin panel and monitoring
- GitHub workflow and commit links

#### Failure Notifications
- ❌ Alert-style HTML template with red styling
- Detailed failure information
- Troubleshooting steps and next actions
- Links to workflow logs and monitoring dashboards
- Issue creation link for tracking

#### Email Recipients
- **Primary:** <EMAIL>
- **From:** Uses the configured SMTP_USERNAME

## Slack Notifications (Backup)

### Configuration Required

Set up the following optional secret for Slack integration:

- **SLACK_WEBHOOK_URL** - Slack incoming webhook URL for #deployments channel

### Slack Features
- Rich block-based messages with status indicators
- Deployment details in a structured format
- Action buttons for quick access to logs and application
- Automatically posts to #deployments channel

## Notification Logic

### When Notifications Are Sent

1. **Success Notifications:**
   - Staging deployment succeeds
   - Production deployment succeeds

2. **Failure Notifications:**
   - Staging deployment fails
   - Production deployment fails

3. **Status Detection:**
   - Automatically determines which environment was deployed
   - Sets appropriate environment URLs and context
   - Handles both success and failure scenarios

### Notification Content

#### Deployment Information Included:
- Environment (staging/production)
- Branch name and commit SHA
- Commit message and author
- Workflow run number and timestamp
- Direct links to application and admin
- Monitoring dashboard links

#### For Failures:
- Detailed error context
- Troubleshooting guidelines
- Links to logs and debugging resources
- Next steps for resolution

## Setup Instructions

### 1. Configure GitHub Secrets

Go to your repository Settings → Secrets and variables → Actions, then add:

```
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-16-character-app-password
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/... (optional)
```

### 2. Gmail Configuration

1. Enable 2-factor authentication on your Gmail account
2. Generate an app-specific password
3. Use this password in the `SMTP_PASSWORD` secret

### 3. Slack Configuration (Optional)

1. Create a Slack app in your workspace
2. Add an incoming webhook
3. Configure it to post to #deployments channel
4. Add the webhook URL to GitHub secrets

## Testing

### Test Email Notifications

1. Make a small change to the develop branch
2. Push to trigger staging deployment
3. Check your email for success/failure notification

### Test Slack Notifications

1. Ensure SLACK_WEBHOOK_URL is configured
2. Deploy to either environment
3. Check #deployments channel for message

## Troubleshooting

### Email Not Received

1. **Check spam folder** - Automated emails often go to spam
2. **Verify secrets** - Ensure SMTP_USERNAME and SMTP_PASSWORD are correct
3. **Gmail security** - Make sure 2FA is enabled and app password is used
4. **Workflow logs** - Check GitHub Actions logs for SMTP errors

### Slack Not Working

1. **Webhook URL** - Verify the Slack webhook URL is correct
2. **Channel permissions** - Ensure the app can post to #deployments
3. **Workspace settings** - Check if external apps are allowed

### Common Issues

1. **App Password vs Regular Password**
   - Always use Gmail app password, never your regular password
   - App passwords are 16 characters without spaces

2. **Environment Detection**
   - Notifications automatically detect staging vs production
   - Based on which deployment job succeeded/failed

3. **Multiple Notifications**
   - One email per deployment (staging or production)
   - Slack notification serves as backup/alternative

## Customization

### Email Templates

Email templates are embedded in the workflow YAML. To customize:

1. Edit the HTML in the `html_body` sections
2. Modify styling, content, or structure as needed
3. Test changes with a deployment

### Additional Recipients

To add more email recipients:

1. Update the `to:` field in both success and failure steps
2. Use comma-separated emails: `<EMAIL>,<EMAIL>`

### Slack Customization

Modify the `custom_payload` section to change:
- Message format and content
- Block layout and styling
- Additional action buttons
- Channel targeting

## Security Considerations

1. **App Passwords** - More secure than regular passwords
2. **Secret Management** - All credentials stored in GitHub secrets
3. **Limited Scope** - Email action only sends, cannot read
4. **Webhook Security** - Slack webhooks are scoped to specific channels

## Monitoring

The notification system includes:
- Automatic status detection
- Error handling and fallbacks
- Workflow logging for debugging
- Multiple notification channels for redundancy
