# Multi-stage Dockerfile for Django application optimized for production

# Base stage with common dependencies
FROM python:3.10-slim AS base
WORKDIR /app

# Install system dependencies needed for all stages
RUN apt-get update && apt-get install -y \
    libpq-dev \
    gcc \
    redis-tools \
    wget \
    curl \
    gnupg \
    ca-certificates \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Doppler CLI for secret management
RUN curl -Ls https://cli.doppler.com/install.sh | sh

# Create non-root user and group
RUN groupadd -g 1000 celery && \
    useradd -u 1000 -g celery -m -s /bin/bash celery

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DOCKER_CONTAINER=1
ENV PYTHONPATH=/app
ENV DJANGO_SETTINGS_MODULE=heibooky.settings

# Dependencies stage - install Python packages
FROM base AS deps

# Copy requirements files
COPY requirements.txt .

# Create a virtual environment for better isolation
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Install Python dependencies with optimizations
# Use production requirements if available, fallback to main requirements.txt
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    (pip install --no-cache-dir -r requirements.txt) && \
    pip cache purge

# Build stage - prepare application files
FROM base AS build
WORKDIR /app

# Copy Python dependencies from deps stage
COPY --from=deps /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy source code (use .dockerignore to exclude unnecessary files)
COPY . .

# Create necessary directories with proper permissions
RUN mkdir -p /app/heibooky/staticfiles \
    /app/heibooky/media \
    /app/heibooky/logs \
    /app/heibooky/static

# Collect static files in build stage for better caching
RUN cd heibooky && \
    python manage.py collectstatic --noinput --clear || echo "Static collection failed, will retry at runtime"

# Run security checks and linting (optional, can be disabled for faster builds)
RUN cd heibooky && \
    python manage.py check --deploy || echo "Deploy check completed with warnings"

# Production stage - final runtime image
FROM base AS production

# Copy Python virtual environment from deps stage
COPY --from=deps /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy application code and prepared files from build stage
COPY --from=build /app .

# Create runtime directories and set permissions
RUN mkdir -p /app/heibooky/staticfiles \
    /app/heibooky/media \
    /app/heibooky/logs \
    /var/run/celery \
    /var/log/celery \
    /var/log/heibooky && \
    touch /var/log/heibooky/django.log \
    /var/log/heibooky/error.log \
    /var/log/heibooky/celery.log && \
    chown -R celery:celery /app \
    /var/run/celery \
    /var/log/celery \
    /var/log/heibooky && \
    chmod -R 755 /app/heibooky/staticfiles \
    /app/heibooky/media \
    /app/heibooky/logs \
    /var/log/heibooky

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/health/ || exit 1

# Expose port
EXPOSE 8000

# Switch to non-root user
USER celery

# Add entrypoint script for better startup handling
COPY --chown=celery:celery scripts/docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Default command for web service with Doppler integration
CMD ["doppler", "run", "--", "docker-entrypoint.sh"]