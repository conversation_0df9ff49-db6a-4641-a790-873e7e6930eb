# Multi-stage Dockerfile for Django application optimized for production
# syntax=docker/dockerfile:1.4

# Base stage with common dependencies - using Alpine for smaller image size (development)
FROM python:3.10-alpine AS base
WORKDIR /app

# Enable BuildKit features for better performance
# DOCKER_BUILDKIT=1 should be set in environment

# Install system dependencies needed for all stages
# Alpine uses apk instead of apt-get and has different package names
RUN apk add --no-cache \
    postgresql-dev \
    gcc \
    musl-dev \
    linux-headers \
    redis \
    wget \
    curl \
    gnupg \
    ca-certificates \
    libffi-dev \
    jpeg-dev \
    zlib-dev \
    freetype-dev \
    lcms2-dev \
    openjpeg-dev \
    tiff-dev \
    tk-dev \
    tcl-dev \
    harfbuzz-dev \
    fribidi-dev \
    libimagequant-dev \
    libxcb-dev \
    libpng-dev

# Install Doppler CLI for secret management (Alpine compatible)
RUN wget -q -t3 'https://packages.doppler.com/public/cli/rsa.8004D9FF50437357.key' -O /etc/apk/keys/<EMAIL> && \
    echo 'https://packages.doppler.com/public/cli/alpine/any-version/main' | tee -a /etc/apk/repositories && \
    apk add --no-cache doppler

# Create non-root user and group (Alpine compatible)
RUN addgroup -g 1000 celery && \
    adduser -u 1000 -G celery -h /home/<USER>/bin/sh -D celery

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DOCKER_CONTAINER=1
ENV PYTHONPATH=/app
ENV DJANGO_SETTINGS_MODULE=heibooky.settings

# Dependencies stage - install Python packages
FROM base AS deps

# Copy requirements files
COPY requirements.txt .

# Create a virtual environment for better isolation
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Install Python dependencies with optimizations and caching
# Use BuildKit cache mount for pip cache
RUN --mount=type=cache,target=/root/.cache/pip \
    --mount=type=cache,target=/tmp/pip-build \
    pip install --upgrade pip setuptools wheel && \
    pip install --find-links https://wheel-index.linuxserver.io/alpine-3.18/ \
    -r requirements.txt

# Build stage - prepare application files with optimized layer caching
FROM base AS build
WORKDIR /app

# Copy Python dependencies from deps stage
COPY --from=deps /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy only requirements and Django settings first for better caching
COPY heibooky/heibooky/settings.py heibooky/heibooky/settings.py
COPY heibooky/manage.py heibooky/manage.py
COPY heibooky/heibooky/__init__.py heibooky/heibooky/__init__.py
COPY heibooky/heibooky/urls.py heibooky/heibooky/urls.py
COPY heibooky/heibooky/wsgi.py heibooky/heibooky/wsgi.py
COPY heibooky/heibooky/asgi.py heibooky/heibooky/asgi.py

# Create necessary directories with proper permissions
RUN mkdir -p /app/heibooky/staticfiles \
    /app/heibooky/media \
    /app/heibooky/logs \
    /app/heibooky/static

# Copy Django apps (this layer will be cached unless app code changes)
COPY heibooky/apps heibooky/apps
COPY heibooky/services heibooky/services
COPY heibooky/templates heibooky/templates

# Copy static files and other assets
COPY heibooky/static heibooky/static

# Copy remaining configuration files
COPY heibooky/pytest.ini heibooky/pytest.ini

# Collect static files in build stage for better caching
RUN cd heibooky && \
    python manage.py collectstatic --noinput --clear || echo "Static collection failed, will retry at runtime"

# Run security checks and linting (optional, can be disabled for faster builds)
RUN cd heibooky && \
    python manage.py check --deploy || echo "Deploy check completed with warnings"

# Runtime base - minimal Alpine image for production
FROM python:3.10-alpine AS runtime-base

# Install only runtime dependencies (using latest stable versions)
RUN apk add --no-cache \
    postgresql-client \
    redis \
    wget \
    curl \
    ca-certificates \
    libpq \
    jpeg \
    zlib \
    freetype \
    lcms2 \
    openjpeg \
    tiff \
    libffi \
    libpng

# Create non-root user and group (Alpine compatible)
RUN addgroup -g 1000 celery && \
    adduser -u 1000 -G celery -h /home/<USER>/bin/sh -D celery

# Install Doppler CLI for secret management (Alpine compatible)
RUN wget -q -t3 'https://packages.doppler.com/public/cli/rsa.8004D9FF50437357.key' -O /etc/apk/keys/<EMAIL> && \
    echo 'https://packages.doppler.com/public/cli/alpine/any-version/main' | tee -a /etc/apk/repositories && \
    apk add --no-cache doppler

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DOCKER_CONTAINER=1
ENV PYTHONPATH=/app
ENV DJANGO_SETTINGS_MODULE=heibooky.settings

WORKDIR /app

# Production stage - final runtime image
FROM runtime-base AS production

# Copy Python virtual environment from deps stage
COPY --from=deps /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy application code and prepared files from build stage
COPY --from=build /app .

# Create runtime directories and set permissions
RUN mkdir -p /app/heibooky/staticfiles \
    /app/heibooky/media \
    /app/heibooky/logs \
    /var/run/celery \
    /var/log/celery \
    /var/log/heibooky && \
    touch /var/log/heibooky/django.log \
    /var/log/heibooky/error.log \
    /var/log/heibooky/celery.log && \
    chown -R celery:celery /app \
    /var/run/celery \
    /var/log/celery \
    /var/log/heibooky && \
    chmod -R 755 /app/heibooky/staticfiles \
    /app/heibooky/media \
    /app/heibooky/logs \
    /var/log/heibooky

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/health/ || exit 1

# Expose port
EXPOSE 8000

# Switch to non-root user
USER celery

# Add entrypoint script for better startup handling
COPY --chown=celery:celery scripts/docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Default command for web service with Doppler integration
CMD ["doppler", "run", "--", "docker-entrypoint.sh"]